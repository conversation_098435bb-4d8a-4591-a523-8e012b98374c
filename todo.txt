// QUESTIONS ?? //
[] - Project: What happens assigned member/pm is removed from company?
[] - Ticket: What happens assigned member / submitter is removed from company?
[] - Ticket: What happens assigned member / submitter is removed from parent project?
[] - Project: When a project is archived, do the tickets get archived too?
[] - Dashboard: Does it change based off role?
[] - Dashboard: Is the dashboard only for admins? (probably not)


// TODO //
[X] - add comments to ticket
[] - Implement permission based features on ticket details page
[X] - edit owned comments
[] - add history to ticket
[] - add attatchements to ticket

[X] - Server & Client: Archive tickets when project is archived

[] - Client: all tickets view for project tickets
[] - Client: sort/filter tickets table
[] - Client: filter tickets table to show resolved tickets

[] - Server: Figure out ticket-member delete behaviors on db

[] - Client: role validation
[] - Client: Project Priority easy change (similar to ticket)
[] - Client: Project remove member modal info/confirmation
[] - Client: Open Tickets Route -> Filter out resolved tickets (HENCE OPEN TICKETS)
[] - Client: Finish Resolved Tickets route
[] - Client: Finish My tickets route
[] - Client: Review and polish routes/breadcrumbs for better UX and navigation



[] - User settings page
[] - Admin: Manage user roles
[] - Admin: Manage company members
[] - Invite codes / emails
[] - Join company via invite
[] - Dynamic Dashboard
[] - Realtime notifications


// TESTS //
[] - Projects Tests
        + Assigning PMs
        + Editing project details / archive
        + Role validations
        + Adding tickets
[] - Tickets Tests
        + Assigning Tickets
        + Editing ticket details(name, priority, status, ect...)
        + Role validations
        + comments
        + history
        + attatchments


// COMPLETE //
[X] - Create new ticket
[X] - Ticket assigned member validation
[X] - Priority, Status, and Type validation middleware or filter
[X] - assign to ticket
[X] - add ticket to project
[X] - change priority & status
[X] - archive ticket
[X] - Server: role validation / member companyId & resource companyId validation
[X] - On remove developer from project: remove from assigned tickets
[X] - Client: archive ticket

