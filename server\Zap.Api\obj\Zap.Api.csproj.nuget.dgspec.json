{"format": 1, "restore": {"C:\\Users\\<USER>\\projects\\zap\\server\\Zap.Api\\Zap.Api.csproj": {}}, "projects": {"C:\\Users\\<USER>\\projects\\zap\\server\\Zap.Api\\Zap.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\projects\\zap\\server\\Zap.Api\\Zap.Api.csproj", "projectName": "Zap.Api", "projectPath": "C:\\Users\\<USER>\\projects\\zap\\server\\Zap.Api\\Zap.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\projects\\zap\\server\\Zap.Api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.400, )"}, "AWSSDK.S3": {"target": "Package", "version": "[3.7.416.5, )"}, "Bogus": {"target": "Package", "version": "[35.6.3, )"}, "FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.1.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[9.0.1, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "dotenv.net": {"target": "Package", "version": "[3.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}